"use client";

import Header from "@/components/dashboard-components/Header";
import WelcomeSection from "@/components/dashboard-components/whistleblower/dashboard/WelcomeSection";
import StatisticsCards from "@/components/dashboard-components/whistleblower/dashboard/StatisticsCards";
import ReportCharts from "@/components/dashboard-components/whistleblower/dashboard/ReportCharts";
import ReportTable from "@/components/dashboard-components/whistleblower/dashboard/ReportTable";
import RecentMessages from "@/components/dashboard-components/whistleblower/dashboard/RecentMessages";
import { useAuth } from "@/hooks/useAuth";
import { useDashboardData } from "@/hooks/useDashboardData";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, FileText, MessageSquareMore } from "lucide-react";


export default function DashboardPage() {
    const { user } = useAuth();
    const dashboardData = useDashboardData();

    return (
        <>
            <div className="w-full h-full">
                <Header onNotificationNavigate={(notification) => {
                    // Use the centralized navigation logic
                    switch (notification.type) {
                        case 'report_update':
                            if (notification.reportId) {
                                dashboardData.navigateToReport(notification.reportId);
                            }
                            break;
                        case 'message':
                            dashboardData.navigateToMessage('');
                            break;
                        case 'system':
                        case 'alert':
                        default:
                            if (notification.actionUrl) {
                                window.location.href = notification.actionUrl;
                            } else {
                                window.location.href = '/dashboard/notifications';
                            }
                            break;
                    }
                }} />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Dashboard overview"
                >
                    {/* Welcome Section */}
                    <section aria-labelledby="welcome-section">
                        <h2 id="welcome-section" className="sr-only">Welcome Section</h2>
                        <WelcomeSection userName={user?.name || user?.firstName || 'User'} />
                    </section>

                    {/* Statistics Cards */}
                    <section aria-labelledby="statistics-section">
                        <h2 id="statistics-section" className="sr-only">Report Statistics</h2>
                        <StatisticsCards
                            stats={dashboardData.stats}
                            isLoading={dashboardData.isLoading}
                        />
                    </section>

                    {/* Charts Section */}
                    <section aria-labelledby="charts-section">
                        <h2 id="charts-section" className="sr-only">Report Analytics Charts</h2>
                        <ReportCharts
                            stats={dashboardData.stats}
                            isLoading={dashboardData.isLoading}
                        />
                    </section>

                    {/* Your Reports Table */}
                    <section aria-labelledby="reports-section">
                        <h2 id="reports-section" className="sr-only">Your Reports</h2>
                        <ReportTable
                            reports={dashboardData.reports}
                            isLoading={dashboardData.isLoading}
                            onReportClick={dashboardData.navigateToReport}
                        />
                    </section>

                    <div className="grid grid-cols-1 xl:grid-cols-3 gap-3 sm:gap-4 md:gap-6 h-full">
                        {/* Recent Messages from Investigators */}
                        <section aria-labelledby="messages-section" className="lg:col-span-2 h-full">
                            <h2 id="messages-section" className="sr-only">Recent Messages</h2>
                            <RecentMessages
                                messages={dashboardData.recentMessages}
                                isLoading={dashboardData.isLoading}
                                onMessageClick={dashboardData.navigateToMessage}
                            />
                        </section>

                        {/* Quick Actions */}
                        <section aria-labelledby="actions-section">
                            <h2 id="actions-section" className="sr-only">Quick Actions</h2>
                            <div className="grid grid-cols-1 gap-4 mb-8">
                                <Link href="/dashboard/whistleblower/new-report">
                                    <Card className="hover:shadow-md transition-shadow cursor-pointer">
                                        <CardContent className="p-6">
                                            <div className="flex items-center gap-4">
                                                <div className="w-12 h-12 bg-[#1E4841] rounded-lg flex items-center justify-center">
                                                    <Plus className="w-6 h-6 text-white" />
                                                </div>
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">Submit New Report</h3>
                                                    <p className="text-sm text-gray-600">Report workplace concerns</p>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </Link>
                                
                                <Link href="/dashboard/whistleblower/my-reports">
                                    <Card className="hover:shadow-md transition-shadow cursor-pointer">
                                        <CardContent className="p-6">
                                            <div className="flex items-center gap-4">
                                                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                                                    <FileText className="w-6 h-6 text-white" />
                                                </div>
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">My Reports</h3>
                                                    <p className="text-sm text-gray-600">View submitted reports</p>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </Link>
                                
                                <Link href="/dashboard/whistleblower/secure-message">
                                    <Card className="hover:shadow-md transition-shadow cursor-pointer">
                                        <CardContent className="p-6">
                                            <div className="flex items-center gap-4">
                                                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                                                    <MessageSquareMore className="w-6 h-6 text-white" />
                                                </div>
                                                <div>
                                                    <h3 className="font-semibold text-gray-900">Secure Messages</h3>
                                                    <p className="text-sm text-gray-600">Communicate securely</p>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </Link>
                            </div>
                        </section>
                    </div>
                </main>
            </div>
        </>
    );
}
