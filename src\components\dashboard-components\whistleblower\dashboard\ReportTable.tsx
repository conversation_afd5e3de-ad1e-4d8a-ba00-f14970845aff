"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Search,
    ListFilter,
    X
} from "lucide-react";
import { DataTable } from "../my-reports/data-table";
import { createColumns } from "./columns";
import { ReportData } from "@/lib/types";
import { ReportDocument } from "@/lib/db/models/interfaces";
import { useAuth } from "@/hooks/useAuth";
import { apiClient } from "@/lib/api/client";
import { transformReportData } from "@/lib/utils/dataTransformers";

interface ReportTableProps {
    reports?: ReportData[];
    isLoading?: boolean;
    onReportClick?: (reportId: string) => void;
}

export default function ReportTable({
    reports: propReports,
    isLoading: propIsLoading,
    onReportClick
}: ReportTableProps = {}) {
    const { user } = useAuth();
    const [reportsData, setReportsData] = useState<ReportData[]>(propReports || []);
    const [isLoading, setIsLoading] = useState(propIsLoading ?? true);
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");

    // Update local state when props change
    useEffect(() => {
        if (propReports) {
            setReportsData(propReports);
        }
    }, [propReports]);

    useEffect(() => {
        if (propIsLoading !== undefined) {
            setIsLoading(propIsLoading);
        }
    }, [propIsLoading]);

    // Load reports data only if not provided via props
    useEffect(() => {
        if (propReports || !user?.id) return;

        const loadReports = async () => {
            try {
                setIsLoading(true);
                const data = await apiClient.get<{ success: boolean; data: ReportDocument[] }>('/api/reports?limit=5');

                if (data.success) {
                    // Transform API data using shared utility
                    const transformedReports = data.data.map(transformReportData);
                    setReportsData(transformedReports);
                }
            } catch (error) {
                console.error('Error loading reports:', error);
                // Fallback to empty array
                setReportsData([]);
            } finally {
                setIsLoading(false);
            }
        };

        loadReports();
    }, [user?.id, propReports]);

    // Filter reports based on search and status
    const filteredReports = reportsData.filter(report => {
        const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            report.id.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = statusFilter === "all" || report.status === statusFilter;
        return matchesSearch && matchesStatus;
    });

    // Get unique statuses for filter dropdown
    const uniqueStatuses = [...new Set(reportsData.map(report => report.status))];

    // Clear all filters
    const clearFilters = () => {
        setSearchTerm("");
        setStatusFilter("all");
    };

    // Check if any filters are active
    const hasActiveFilters = searchTerm !== "" || statusFilter !== "all";

    return (
        <Card className="gap-0">
            <CardHeader className="flex flex-col lg:flex-row items-center justify-between gap-4">
                <div>
                    <CardTitle className="text-base sm:text-lg">Your Reports</CardTitle>
                </div>
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                            placeholder="Search reports..."
                            name="Search Reports"
                            className="pl-10 w-full md:w-42 lg:w-64 py-5"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                    <div className="flex items-center gap-2">
                        <Select
                            name="Report Status"
                            value={statusFilter} onValueChange={setStatusFilter}
                            aria-label="Filter by report status"
                        >
                            <SelectTrigger className="w-full md:w-36 lg:w-48 py-5 text-sm font-normal text-[#1E4841] border shadow-xs">
                                <SelectValue placeholder="All Statuses" />
                            </SelectTrigger>
                            <SelectContent>
                                {hasActiveFilters && (
                                    <Button
                                        aria-label="Clear Filters"
                                        variant="outline"
                                        size="default"
                                        className="py-5 text-sm font-normal text-[#1E4841]"
                                        onClick={clearFilters}
                                    >
                                        <X className="w-4 h-4 mr-1" />
                                        Clear
                                    </Button>
                                )}
                                <SelectItem value="all">All Statuses</SelectItem>
                                {uniqueStatuses.map(status => (
                                    <SelectItem key={status} value={status}>{status}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <Button
                            aria-label="Filter Reports"
                            variant="outline"
                            size="default"
                            className="py-5 text-sm font-normal text-[#1E4841] flex w-fit">
                            <ListFilter />
                        </Button>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="w-full p-6">
                {isLoading ? (
                    <div className="space-y-4">
                        {[...Array(5)].map((_, index) => (
                            <div key={index} className="flex items-center gap-4 p-4 border rounded-lg animate-pulse">
                                <div className="h-4 bg-gray-200 rounded w-20"></div>
                                <div className="h-4 bg-gray-200 rounded flex-1"></div>
                                <div className="h-4 bg-gray-200 rounded w-24"></div>
                                <div className="h-4 bg-gray-200 rounded w-16"></div>
                                <div className="h-4 bg-gray-200 rounded w-20"></div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <DataTable columns={createColumns(onReportClick)} data={filteredReports} />
                )}
            </CardContent>
        </Card>
    );
}