"use client";

import { useState } from 'react';
import { apiClient } from '@/lib/api/client';

export default function DebugApiPage() {
  const [results, setResults] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const testEndpoint = async (name: string, endpoint: string) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    try {
      console.log(`Testing ${name}: ${endpoint}`);
      const result = await apiClient.get(endpoint);
      console.log(`${name} result:`, result);
      setResults(prev => ({ ...prev, [name]: { success: true, data: result } }));
    } catch (error) {
      console.error(`${name} error:`, error);
      setResults(prev => ({ ...prev, [name]: { success: false, error: error.message } }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const testAuth = () => {
    const token = localStorage.getItem('auth_token');
    console.log('Auth token exists:', !!token);
    console.log('Auth token value:', token ? token.substring(0, 20) + '...' : 'null');
    setResults(prev => ({
      ...prev,
      auth: {
        hasToken: !!token,
        tokenPreview: token ? token.substring(0, 20) + '...' : 'null'
      }
    }));
  };

  const testLogin = async () => {
    setLoading(prev => ({ ...prev, login: true }));
    try {
      const response = await fetch('/api/auth/login/whistleblower', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'whistleblower123',
          remember: false
        })
      });

      const result = await response.json();
      console.log('Login result:', result);

      if (result.success && result.token) {
        localStorage.setItem('auth_token', result.token);
        setResults(prev => ({ ...prev, login: { success: true, message: 'Login successful!' } }));
      } else {
        setResults(prev => ({ ...prev, login: { success: false, error: result.error || 'Login failed' } }));
      }
    } catch (error) {
      console.error('Login error:', error);
      setResults(prev => ({ ...prev, login: { success: false, error: error.message } }));
    } finally {
      setLoading(prev => ({ ...prev, login: false }));
    }
  };

  const endpoints = [
    { name: 'notifications', endpoint: '/api/notifications?limit=5' },
    { name: 'reports', endpoint: '/api/reports' },
    { name: 'conversations', endpoint: '/api/conversations?limit=5' },
    { name: 'dashboard-stats', endpoint: '/api/dashboard/stats' },
    { name: 'recent-activity', endpoint: '/api/dashboard/recent-activity?limit=5' },
  ];

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">API Debug Page</h1>
      
      <div className="mb-6 space-x-4">
        <button
          onClick={testAuth}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Check Auth Status
        </button>
        <button
          onClick={testLogin}
          disabled={loading.login}
          className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:opacity-50"
        >
          {loading.login ? 'Logging in...' : 'Login as Test User'}
        </button>
      </div>

      {results.auth && (
        <div className="mb-4 p-4 border rounded bg-gray-50">
          <h3 className="font-semibold mb-2">Auth Status:</h3>
          <pre className="text-xs">{JSON.stringify(results.auth, null, 2)}</pre>
        </div>
      )}

      {results.login && (
        <div className="mb-4 p-4 border rounded bg-gray-50">
          <h3 className="font-semibold mb-2">Login Result:</h3>
          <pre className="text-xs">{JSON.stringify(results.login, null, 2)}</pre>
        </div>
      )}

      <div className="grid gap-4">
        {endpoints.map(({ name, endpoint }) => (
          <div key={name} className="border p-4 rounded">
            <div className="flex items-center gap-4 mb-2">
              <h3 className="font-semibold">{name}</h3>
              <button
                onClick={() => testEndpoint(name, endpoint)}
                disabled={loading[name]}
                className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 disabled:opacity-50"
              >
                {loading[name] ? 'Testing...' : 'Test'}
              </button>
            </div>
            <p className="text-sm text-gray-600 mb-2">{endpoint}</p>
            {results[name] && (
              <div className="bg-gray-100 p-2 rounded">
                <pre className="text-xs overflow-auto">
                  {JSON.stringify(results[name], null, 2)}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-6">
        <button
          onClick={() => {
            endpoints.forEach(({ name, endpoint }) => {
              testEndpoint(name, endpoint);
            });
          }}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
        >
          Test All Endpoints
        </button>
      </div>
    </div>
  );
}
