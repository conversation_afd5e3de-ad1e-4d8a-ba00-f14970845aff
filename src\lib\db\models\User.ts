import { Schema } from 'mongoose';
import { createModel } from '../utils';

const UserPreferencesSchema = new Schema({
  language: { type: String, default: 'English' },
  timezone: { type: String, default: 'UTC' },
  notifications: {
    email: { type: Boolean, default: true },
    push: { type: Boolean, default: true },
    sms: { type: Boolean, default: false },
    reportUpdates: { type: Boolean, default: true },
    systemAlerts: { type: Boolean, default: true },
    urgentReports: { type: Boolean, default: true },
    weeklyDigest: { type: Boolean, default: true }
  },
  theme: { type: String, enum: ['light', 'dark', 'system'], default: 'light' },
  systemSettings: {
    autoAssignReports: { type: Boolean, default: false },
    enableRealTimeNotifications: { type: Boolean, default: true },
    showAdvancedAnalytics: { type: Boolean, default: true },
    allowBulkActions: { type: Boolean, default: false }
  },
  privacySettings: {
    profileVisibility: {
      type: String,
      enum: ['private', 'limited', 'public'],
      default: 'private'
    },
    allowDirectContact: { type: Boolean, default: false },
    shareReportingHistory: { type: Boolean, default: false }
  }
});

const TwoFactorSchema = new Schema({
  enabled: { type: Boolean, default: false, required: true },
  method: { type: String, enum: ['email', 'app'], default: 'email', required: true },
  secret: { type: String },
  backupCodes: [{ type: String }],
  verificationCode: { type: String },
  verificationCodeExpires: { type: Date },
  attempts: { type: Number, default: 0 }
});

const SecuritySettingsSchema = new Schema({
  sessionTimeout: { type: Number, default: 30 }, // minutes
  ipRestriction: { type: Boolean, default: false },
  allowedIPs: [{ type: String }],
  auditLogging: { type: Boolean, default: true },
  lastPasswordChange: { type: Date },
  passwordHistory: [{
    hash: String,
    changedAt: { type: Date, default: Date.now }
  }]
});

const UserSchema = new Schema({
  email: { type: String, required: true, unique: true },
  hashedPassword: { type: String },
  firstName: { type: String },
  lastName: { type: String },
  companyId: { type: Schema.Types.ObjectId, ref: 'Company' },
  role: {
    type: String,
    enum: ['whistleblower', 'investigator', 'admin'],
    required: true
  },

  // Profile Information
  phoneNumber: { type: String },
  department: { type: String },
  jobTitle: { type: String },
  employeeId: { type: String },
  profileImage: { type: String },

  // Admin specific fields
  adminLevel: {
    type: String,
    enum: ['Standard', 'Senior', 'Lead', 'Manager', 'Director', 'Super Admin'],
    default: 'Standard'
  },
  permissions: [{ type: String }],

  // Account Status
  isActive: { type: Boolean, default: true },
  lastLogin: { type: Date },

  // Settings and Preferences
  preferences: { type: UserPreferencesSchema, default: {} },
  twoFactor: { type: TwoFactorSchema, default: {} },
  securitySettings: { type: SecuritySettingsSchema, default: {} },

  // Security and Authentication
  passwordResetToken: { type: String },
  passwordResetExpires: { type: Date },
  emailVerified: { type: Boolean, default: false },
  emailVerificationToken: { type: String },
  failedLoginAttempts: { type: Number, default: 0 },
  accountLocked: { type: Boolean, default: false },
  accountLockedUntil: { type: Date },
  unlockToken: { type: String },
  unlockTokenExpires: { type: Date },
  passwordNeedsMigration: { type: Boolean, default: false },
  passwordHashAlgorithm: { type: String, enum: ['sha256', 'bcrypt'], default: 'bcrypt' }
}, {
  timestamps: true
});

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  if (this.firstName && this.lastName) {
    return `${this.firstName} ${this.lastName}`;
  }
  return this.email.split('@')[0];
});

// Method to check if account is locked
UserSchema.methods.isAccountLocked = function() {
  if (!this.accountLocked) return false;
  if (this.accountLockedUntil && new Date() > this.accountLockedUntil) {
    this.accountLocked = false;
    this.accountLockedUntil = undefined;
    this.failedLoginAttempts = 0;
    return false;
  }
  return true;
};

// Use the utility function to create the model safely
const User = createModel('User', UserSchema);

export default User;