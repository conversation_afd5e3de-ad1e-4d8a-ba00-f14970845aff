import { NextRequest, NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export const runtime = 'nodejs';

// Configure maximum file size (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Allowed file types for evidence
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'application/pdf',
  'text/plain',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'video/mp4',
  'video/quicktime',
  'audio/mpeg',
  'audio/wav'
];

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    
    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No files provided' },
        { status: 400 }
      );
    }
    
    // Validate files
    for (const file of files) {
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { success: false, error: `File ${file.name} exceeds maximum size of 10MB` },
          { status: 400 }
        );
      }
      
      if (!ALLOWED_MIME_TYPES.includes(file.type)) {
        return NextResponse.json(
          { success: false, error: `File type ${file.type} is not allowed` },
          { status: 400 }
        );
      }
    }
    
    const uploadedFiles = [];
    
    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'uploads', 'evidence');
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }
    
    for (const file of files) {
      try {
        // Generate unique filename
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const fileExtension = file.name.split('.').pop();
        const fileName = `${timestamp}_${randomString}.${fileExtension}`;
        const filePath = join(uploadsDir, fileName);
        
        // Convert file to buffer and save
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);
        
        // Create file metadata
        const fileMetadata = {
          fileName: fileName,
          originalName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          fileUrl: `/uploads/evidence/${fileName}`,
          uploadedAt: new Date(),
          uploadedBy: request.user.id,
          description: formData.get(`description_${file.name}`) as string || '',
          isEncrypted: false // TODO: Implement encryption
        };
        
        uploadedFiles.push(fileMetadata);
      } catch (fileError) {
        console.error(`Error uploading file ${file.name}:`, fileError);
        return NextResponse.json(
          { success: false, error: `Failed to upload file ${file.name}` },
          { status: 500 }
        );
      }
    }
    
    return NextResponse.json({
      success: true,
      data: uploadedFiles,
      message: `Successfully uploaded ${uploadedFiles.length} file(s)`
    });
  } catch (error) {
    console.error('Upload evidence API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload files' },
      { status: 500 }
    );
  }
});

// GET endpoint to retrieve file information
export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('fileName');
    
    if (!fileName) {
      return NextResponse.json(
        { success: false, error: 'File name is required' },
        { status: 400 }
      );
    }
    
    // TODO: Implement file retrieval with proper access control
    // For now, return basic file info
    return NextResponse.json({
      success: true,
      data: {
        fileName,
        fileUrl: `/uploads/evidence/${fileName}`,
        message: 'File access would be implemented with proper security checks'
      }
    });
  } catch (error) {
    console.error('Get evidence file API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve file' },
      { status: 500 }
    );
  }
});

// DELETE endpoint to remove uploaded files
export const DELETE = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    const { fileName } = await request.json();
    
    if (!fileName) {
      return NextResponse.json(
        { success: false, error: 'File name is required' },
        { status: 400 }
      );
    }
    
    // TODO: Implement file deletion with proper access control
    // Should verify that the user has permission to delete the file
    
    return NextResponse.json({
      success: true,
      message: 'File deletion would be implemented with proper security checks'
    });
  } catch (error) {
    console.error('Delete evidence file API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete file' },
      { status: 500 }
    );
  }
});
