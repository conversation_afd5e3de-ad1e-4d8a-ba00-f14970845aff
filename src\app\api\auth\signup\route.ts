import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { signUpSchema } from '@/lib/schemas';
import { HARDCODED_USERS } from '@/lib/auth/hardcoded-users';
import { generateToken } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = signUpSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { fullName, email, phoneNumber, password, role = 'whistleblower', companyId } = validationResult.data;

    // Check if user already exists in hardcoded users
    const existingHardcodedUser = HARDCODED_USERS.find(user => user.email === email);
    if (existingHardcodedUser) {
      return NextResponse.json(
        { success: false, error: 'User already exists' },
        { status: 409 }
      );
    }

    // Check if user already exists in database
    await connectDB();
    const existingDbUser = await DataService.getUserByEmail(email);
    if (existingDbUser) {
      return NextResponse.json(
        { success: false, error: 'User already exists' },
        { status: 409 }
      );
    }

    // Split full name into first and last name
    const nameParts = fullName.trim().split(' ');
    const firstName = nameParts[0];
    const lastName = nameParts.slice(1).join(' ') || '';

    // Create user in database
    const newUser = await DataService.createUser({
      email,
      firstName,
      lastName,
      phoneNumber,
      password,
      role: role as 'admin' | 'investigator' | 'whistleblower',
      companyId: companyId || undefined,
      isActive: true
    });

    // Create welcome conversation and message for new user
    await createWelcomeMessage(newUser._id.toString(), role);

    // Generate JWT token
    const token = generateToken(
      newUser._id.toString(),
      (newUser as unknown as { role: string }).role,
      (newUser as unknown as { companyId?: string }).companyId
    );

    // Return success response (without password)
    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      token,
      user: {
        id: newUser._id,
        email: (newUser as unknown as { email: string }).email,
        firstName: (newUser as unknown as { firstName: string }).firstName,
        lastName: (newUser as unknown as { lastName: string }).lastName,
        role: (newUser as unknown as { role: string }).role,
        companyId: (newUser as unknown as { companyId?: string }).companyId,
        isActive: (newUser as unknown as { isActive: boolean }).isActive
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Signup error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function createWelcomeMessage(userId: string, role: string) {
  try {
    // Find admin user to send welcome message from
    const adminUser = await DataService.getUserByEmail('<EMAIL>');
    if (!adminUser) {
      console.warn('Admin user not found, skipping welcome message');
      return;
    }

    // Create a dummy report for the conversation (required by the schema)
    const welcomeReport = await DataService.createReport({
      title: `Welcome to 7IRIS - ${role} Account`,
      description: 'Welcome message conversation',
      category: 'Other',
      priority: 'Low',
      userId: new mongoose.Types.ObjectId(userId),
      status: 'New',
      isAnonymous: false
    });

    // Create conversation between admin and new user
    const conversation = await DataService.createConversation({
      reportId: welcomeReport._id.toString(),
      participants: [adminUser._id.toString(), userId]
    });

    // Create welcome message based on role
    let welcomeContent = '';
    if (role === 'whistleblower') {
      welcomeContent = `Welcome to 7IRIS Whistleblowing Platform! 🎉

Thank you for joining our secure platform. Here's what you can do:

• Submit reports safely and securely
• Communicate with investigators through encrypted messaging
• Track the progress of your reports
• Maintain your anonymity when needed

Your safety and privacy are our top priorities. If you have any questions, feel free to reach out.

Best regards,
7IRIS Admin Team`;
    } else if (role === 'admin') {
      welcomeContent = `Welcome to 7IRIS Admin Portal! 🎉

Thank you for joining as an administrator. Here are your capabilities:

• Manage and review all reports
• Assign investigators to cases
• Monitor platform activity and statistics
• Communicate with users through secure messaging
• Access administrative tools and settings

If you need any assistance getting started, please don't hesitate to ask.

Best regards,
7IRIS Admin Team`;
    } else {
      welcomeContent = `Welcome to 7IRIS Platform! 🎉

Thank you for joining our secure platform. You now have access to:

• Secure messaging system
• Report management tools
• Real-time notifications
• Encrypted communications

If you have any questions about using the platform, feel free to reach out.

Best regards,
7IRIS Admin Team`;
    }

    // Send welcome message
    await DataService.createMessage({
      conversationId: conversation._id.toString(),
      senderId: adminUser._id.toString(),
      content: welcomeContent,
      messageType: 'text'
    });

    console.log(`Welcome message sent to new ${role} user: ${userId}`);
  } catch (error) {
    console.error('Error creating welcome message:', error);
    // Don't throw error as this is not critical for signup
  }
}